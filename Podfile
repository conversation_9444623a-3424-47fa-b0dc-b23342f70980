source 'ssh://*******************/ios/specs.git'

xcodeproj 'ipaymentapp/ipaymentapp.project'

inhibit_all_warnings!

platform :ios, '12.0'

plugin 'cocoapods-xcfilelist-patch'
plugin 'cocoapods-react-native-mrn'
plugin 'cocoapods-binary'

target "ipaymentapp" do

# ipaymentApp 特有，imeituan 不会引用
binary_pod 'ReactiveViewModel', '0.3'
pod 'LookinServer', :configurations => ['Debug', 'ReleaseInhouse', 'DailyBuild']
pod 'SAKFinanceUIKit', :git => 'ssh://*******************/payios/sakfinanceuikit.git', :branch => 'development'
pod 'SAKFinBusiness', :git =>'ssh://*******************/payios/sakfinbusiness.git', :branch => 'master', :subspecs => ['PayDemo']

# 同步美团相关依赖，列表内所有组件的依赖都会同步
sync_with 'ssh://*******************/ios/imeituan.git', branch: 'release/12.32.200', pods: %w{
  SAKWallet
  SAKHybridCashier
  SAKNeoHybrid
  SAKHybridMeituanPay
  SAKKQClient
  SAKOneClickPayCashier
  SAKPaymentChannel
  SAKMeituanPay
  SAKPaymentKit
  SAKPaymentGuard
  SAKBankCardRecognizer
  SAKIdentityCardRecognizer
  SAKFinVerificationCenter
  SAKFinanceThirdPayAdpater
  SAKFinPicassoModule
  SAKPaymentAdapter
  SAKEnhancedHybrid
  SAKFinanceDynamic
  SAKLivenessDetection
  MTSFBarcodeCashier
  uppay

  SAKQRCodeScanner
  PSPushService
  CIPAssertionHandler
  WaiMaiDebugKit
  
  MRN
  MRNNativeComponents
  MRNCommonAPI
  MRNNested
  react-native-netinfo
  react-native-webview
  react-native-maskedview
  RNSVG
  BVLinearGradient
  RNGestureHandler
}

# pod-sync-version:begin
binary_pod 'AFNetworking', '2.6.220'
binary_pod 'APMLogger', '0.0.8'
binary_pod 'AutoCoding', '2.2.1'
binary_pod 'BVLinearGradient', '*******'
binary_pod 'BabelClient', '0.0.12'
binary_pod 'BaseModel', '1.0.21'
binary_pod 'BaseModule', '2.6.12'
binary_pod 'BindingX', '*******'
binary_pod 'CFCASDK', '*******'
binary_pod 'CGCipherLib', '1.0.1'
binary_pod 'CIPABTest', '2.0.11'
binary_pod 'CIPAnimPlayer', '0.0.52'
binary_pod 'CIPAppState', '1.0.22'
binary_pod 'CIPAssertionHandler', '1.0.12'
binary_pod 'CIPAssetPluginManger', '12.21.200'
binary_pod 'CIPFSPService', '0.2.170'
binary_pod 'CIPFoundation', '1.12.53'
binary_pod 'CIPKeychain', '0.0.9'
binary_pod 'CIPIconFont', '0.0.10'
binary_pod 'CIPLaunchProtect', '2.0.26'
binary_pod 'CIPMetrics', '7.0.258'
binary_pod 'CIPPrivacy', '*********'
binary_pod 'CIPPrivacyBridge', '0.1.9'
binary_pod 'CIPPrivacyProtocol', '1.0.12'
binary_pod 'CIPSSIDUse', '0.1.16'
binary_pod 'CIPServiceRegistry', '0.1.221'
binary_pod 'CIPStorage', '1.9.39'
binary_pod 'CIPStorageMetrics', '1.3.56'
binary_pod 'CSSocketIO', '0.1.11'
binary_pod 'CocoaAsyncSocket', '*******'
binary_pod 'CocoaLumberjack', '2.2.0'
binary_pod 'CoverageKit', '0.2.66'
binary_pod 'DSActivityView', '1.0.0'
binary_pod 'DXCoreKit', '4.49.205'
binary_pod 'DXUISDK', '4.49.225'
binary_pod 'Dolphin', '0.2.8'
binary_pod 'DoubleConversion', '*******'
binary_pod 'EHComponentService', '4.0.7'
binary_pod 'EHPlugins', '4.0.4'
binary_pod 'EdfuCardScanner', '2.18.1'
binary_pod 'EdfuCommonProtocol', '0.0.2'
binary_pod 'EdfuCore', '2.4.0'
binary_pod 'EdfuFleacv', '*******'
binary_pod 'EdfuMBar', '1.34.2'
binary_pod 'EdfuMlens', '0.31.0'
binary_pod 'FLEX', '4.4.3'
binary_pod 'FMDB', '*******'
binary_pod 'Folly', '2018.10.22.02'
binary_pod 'GCBaseModule', '2.11.26'
binary_pod 'GCDWebServer', '3.4.2', :subspecs => ['Core']
binary_pod 'GLog', '*******'
binary_pod 'Gryphon', '0.1.6'
binary_pod 'Kylin', '1.4.8'
binary_pod 'LegoBase', '*********'
binary_pod 'LogReportSwitcher', '0.6.3'
binary_pod 'METBundleService', '0.1.43'
binary_pod 'METDIOBundle', '1.1.8'
binary_pod 'METServiceRegistry', '12.31.400'
binary_pod 'METSharedResource', '12.1.21'
binary_pod 'METToolkits', '1.1.6'
binary_pod 'MLeaksFinder', '1.1.208'
binary_pod 'MMKV', '1.0.17.10'
binary_pod 'MMapLocationServiceKit', '1.1228.0'
binary_pod 'MMapSearchKit', '1.1228.1'
binary_pod 'MRDLocationManager', '2.1232.0'
binary_pod 'MRN', '3.60.0'
binary_pod 'MRNCommonAPI', '0.0.19'
binary_pod 'MRNDataPrefetch', '1.0.16'
binary_pod 'MRNDebugKit', '3.12.23'
binary_pod 'MRNNativeComponents', '3.7.36'
binary_pod 'MRNNativeModules', '3.0.106'
binary_pod 'MRNNested', '0.0.11'
binary_pod 'MSC', '1.62.4'
binary_pod 'MSCDebugKit', '0.1.36'
binary_pod 'MSCWTF', '0.1.24'
binary_pod 'MSI', '12.32.201'
binary_pod 'MSIAPIMetrics', '0.0.11'
binary_pod 'MSIInputComponent', '12.30.200'
binary_pod 'MSIMetricsBaseAdaptor', '0.0.4'
binary_pod 'MSITextAreaComponent', '12.30.200'
binary_pod 'MSITteBaseBizAdaptor', '0.0.1'
binary_pod 'MTAudioSession', '0.0.45'
binary_pod 'MTCookieManager', '1.0.1'
binary_pod 'MTDXSDK', '4.49.210'
binary_pod 'MTMapFoundationKit', '4.1228.0'
binary_pod 'MTPlayerComponents', '12.30.200.2'
binary_pod 'MTRotateCompat', '1.0.6'
binary_pod 'MTSFBarcodeCashier', '11.9.1'
binary_pod 'MainBoard', '1.0.54.2'
binary_pod 'Mantle', '1.5.8.8'
binary_pod 'Masonry', '0.6.4.1'
binary_pod 'Midas', '1.5.98'
binary_pod 'NVCodeLogger', '0.3.7'
binary_pod 'NVDebugNetworkAgent', '0.8.10'
binary_pod 'NVJSON', '0.1.11'
binary_pod 'NVJsonLabel', '11.0.85.2'
binary_pod 'NVLayout', '1.2.9'
binary_pod 'NVLinker', '0.1.47'
binary_pod 'NVModelLogic', '1.4.91.16'
binary_pod 'NVMonitorCenter', '4.5.18'
binary_pod 'NVNetwork', '1.1.22'
binary_pod 'NVNetworkCore', '4.9.20'
binary_pod 'NVNetworkLogger', '5.6.25'
binary_pod 'NVObject', '0.2.9.2'
binary_pod 'NVPopoverView', '1.1.9'
binary_pod 'NVReachability', '0.6.7'
binary_pod 'NVSharkPush', '3.0.7'
binary_pod 'NVSmartShark', '0.1.10'
binary_pod 'NVTaskJson', '0.0.7'
binary_pod 'NVTaskMapi', '3.0.11'
binary_pod 'NVTunnel', '4.8.8'
binary_pod 'NVVoiceOver', '11.20.10.6'
binary_pod 'NetBusiness', '12.31.200'
binary_pod 'OWL', '2.4.73'
binary_pod 'Onimaru', '1.10.76'
binary_pod 'OpenSSL-Universal', '1.0.2.1'
binary_pod 'PGAKit', '2.11.73'
binary_pod 'PSPushService', '2.0.73'
binary_pod 'Picasso', '2.50.12'
binary_pod 'PicassoCache', '1.48.2'
binary_pod 'Pike', '1.3.54'
binary_pod 'PikeLiveModule', '1.0.12'
binary_pod 'QHOServiceManager', '0.3.12'
binary_pod 'RNGestureHandler', '1.10.3.1'
binary_pod 'RNSVG', '12.1.0.6'
binary_pod 'RPJSONValidator', '0.2.9'
binary_pod 'React', '0.63.3.147'
binary_pod 'React-jsi', '0.63.3.18'
binary_pod 'ReactiveCocoa', '2.5.133'
binary_pod 'RooDesign', '8.44.0'
binary_pod 'SAKAccount', '5.91.1'
binary_pod 'SAKAccountStandard', '0.1.4'
binary_pod 'SAKBabelTower', '1.4.53'
binary_pod 'SAKBacktracer', '0.7.0'
binary_pod 'SAKBankCardRecognizer', '12.4.1'
binary_pod 'SAKBaseModel', '4.0.109'
binary_pod 'SAKBeetles', '3.3.33'
binary_pod 'SAKBus', '1.0.9'
binary_pod 'SAKCardNFCRecognizer', '0.4.1'
binary_pod 'SAKCashier', '13.0.5'
binary_pod 'SAKCatMonitor', '3.0.106'
binary_pod 'SAKCrash', '1.22.75'
binary_pod 'SAKCrypto', '3.0.12'
binary_pod 'SAKCustomDNS', '2.1.40'
binary_pod 'SAKDDD', '1.7.39'
binary_pod 'SAKDDDMSCAdaptor', '0.2.5'
binary_pod 'SAKDebugCenter', '0.0.12'
binary_pod 'SAKDebugKit', '4.6.39'
binary_pod 'SAKDomainObject', '1.0.0'
binary_pod 'SAKEnhancedHybrid', '8.5.5'
binary_pod 'SAKEnvironment', '1.0.49.2'
binary_pod 'SAKExceptionKit', '7.72.19'
binary_pod 'SAKFactoryConfigurator', '0.0.5'
binary_pod 'SAKFetchedResultsController', '0.0.4'
binary_pod 'SAKFinVerificationCenter', '12.7.1.1'
binary_pod 'SAKFinanceThirdPayAdpater', '12.0.1'
binary_pod 'SAKGuard', '6.6.3'
binary_pod 'SAKHorn', '2.0.32'
binary_pod 'SAKHybridCashier', '13.0.0.1'
binary_pod 'SAKHybridMeituanPay', '12.2.1'
binary_pod 'SAKIdentityCardRecognizer', '12.8.1'
binary_pod 'SAKKQClient', '0.0.14'
binary_pod 'SAKMeituanPay', '12.7.1.1'
binary_pod 'SAKMemoryLeakMonitor', '0.2.3'
binary_pod 'SAKMesh', '0.4.2'
binary_pod 'SAKMetrics', '1.10.1'
binary_pod 'SAKNavigationBarTransition', '1.0.8'
binary_pod 'SAKNeoCore', '1.22.0'
binary_pod 'SAKNeoHybrid', '1.17.0'
binary_pod 'SAKNetwork', '3.12.89'
binary_pod 'SAKOneClickPayCashier', '12.2.0'
binary_pod 'SAKPayRouter', '0.0.15'
binary_pod 'SAKPaymentAdapter', '11.9.1'
binary_pod 'SAKPaymentChannel', '12.3.1'
binary_pod 'SAKPaymentGuard', '1.7.0'
binary_pod 'SAKPaymentKit', '13.0.5'
binary_pod 'SAKPersistentConnect', '0.6.15'
binary_pod 'SAKPickup', '2.0.38'
binary_pod 'SAKPlatform', '12.29.402'
binary_pod 'SAKPortal', '4.3.12'
binary_pod 'SAKQRCodeScanner', '1.20.0'
binary_pod 'SAKRecce', '1.26.1'
binary_pod 'SAKRisk', '0.2.21'
binary_pod 'SAKSandboxManager', '1.0.2'
binary_pod 'SAKService', '0.4.8'
binary_pod 'SAKSniffer', '1.4.16'
binary_pod 'SAKStatistics', '4.105.0'
binary_pod 'SAKStatisticsMRNModule', '0.2.0'
binary_pod 'SAKStorage', '3.1.3'
binary_pod 'SAKSwizzle', '0.2.8'
binary_pod 'SAKUIKit', '4.3.45'
binary_pod 'SAKURLProtocol', '2.0.0'
binary_pod 'SAKWallet', '12.2.0'
binary_pod 'SAKWebView', '5.2.8'
binary_pod 'SDWebImage', '5.14.243', :inhibit_warnings => true
binary_pod 'SSZipArchive', '0.5.3'
binary_pod 'SankuaiKit', '11.0.6'
binary_pod 'SocketRocket', '0.5.1'
binary_pod 'StaticTunnel', '2.0.18'
binary_pod 'TKAlertCenter', '0.3.5'
binary_pod 'TMCache', '1.5.4'
binary_pod 'TNSecureProtocol', '1.9.1'
binary_pod 'TOSKit', '0.1.59'
binary_pod 'TTEEncryption', '1.2.1'
binary_pod 'TTTAttributedLabel', '1.13.11'
binary_pod 'TableSeparator', '0.4.10'
binary_pod 'Titans', '20.28.2'
binary_pod 'TitansCore', '0.6.91'
binary_pod 'TitansCoreJSBridge', '20.25.42'
binary_pod 'TitansOffline', '0.1.32'
binary_pod 'TitansProtocol', '0.0.20'
binary_pod 'TitansWidget', '0.4.10'
binary_pod 'Vane-iOS', '0.4.2'
binary_pod 'WMCTKit', '0.4.33'
binary_pod 'WMMach', '12.32.202'
binary_pod 'WMPhoenixKit', '1.0.31'
binary_pod 'WMPixar', '0.0.5'
binary_pod 'WMRocks', '8.47.3'
binary_pod 'WaiMaiDebugKit', '1.2.95'
binary_pod 'Whale', '1.7.10'
binary_pod 'WhiteBoard', '1.4.6'
binary_pod 'Yoda', '***********'
binary_pod 'boost-for-react-native', '********'
binary_pod 'cip-alipay', '15.8.16'
binary_pod 'clogan', '********'
binary_pod 'edfu-MNN', '0.1.21'
binary_pod 'edfulib', '2.8.0'
binary_pod 'facedetection', '*******'
binary_pod 'fishhook', '0.2.18'
binary_pod 'informer', '1.1.21'
binary_pod 'libextobjc', '*******'
binary_pod 'libwebp', '1.0.0'
binary_pod 'lottie-ios', '*******'
binary_pod 'mrn-jsi', '********'
binary_pod 'ntp', '1.0.21'
binary_pod 'quickjs', '0.0.10'
binary_pod 'react-native-maskedview', '0.2.0'
binary_pod 'react-native-netinfo', '********'
binary_pod 'react-native-webview', '********'
binary_pod 'sharkhpack', '1.1.3'
binary_pod 'tencentQQ', '********'
binary_pod 'uppay', '3.6.2'
binary_pod 'wechat', '2.0.4'
binary_pod 'yoga', '0.60.7'
binary_pod 'zstd', '1.0.6'
# pod-sync-version:end
binary_pod 'SAKLivenessDetection', '1.0.5'

binary_pod 'RecceDebugKit', '1.26.7'

binary_pod 'SAKFinLogger', '0.1.9'

binary_pod 'SAKReccePlugin', '1.26.0', :subspecs => ["Core"]

binary_pod 'SAKNeoService', '********-prod', :subspecs => ["MT"]

binary_pod 'SAKNeoWidget', '1.18.0'


end

target "ipaymentappTests" do
    pod 'Specta', '1.0.6'
    pod 'Expecta', '~> 0.3.0'
    pod 'OCMock', '~> 2.2.4'
    pod 'OHHTTPStubs', '3.1.12'
    pod 'KIF'
end

post_install do |installer|
    copy_pods_resources_path = "Pods/Target Support Files/Pods-ipaymentapp/Pods-ipaymentapp-resources.sh"
    string_to_replace = '--compile "${BUILT_PRODUCTS_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}"'
    assets_compile_with_app_icon_arguments = '--compile "${BUILT_PRODUCTS_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}" --app-icon "${ASSETCATALOG_COMPILER_APPICON_NAME}" --output-partial-info-plist "${BUILD_DIR}/assetcatalog_generated_info.plist"'
    text = File.read(copy_pods_resources_path)
    new_contents = text.gsub(string_to_replace, assets_compile_with_app_icon_arguments)
    File.open(copy_pods_resources_path, "w") {|file| file.puts new_contents }
    installer.pods_project.targets.each do |target|
        target.build_configurations.each do |config|
            config.build_settings['GCC_TREAT_WARNINGS_AS_ERRORS'] = 'NO'
            if target.name == 'SAKWallet' or target.name == 'SAKCashier' or target.name == 'SAKPaymentChannel' or target.name == 'SAKMeituanPay' or target.name == 'SAKPaymentKit' or target.name == 'SAKPaymentGuard' or target.name == 'SAKPaymentAccount' or target.name == 'SAKFinBusiness' or target.name == 'SAKFinanceThirdPayAdpater' or target.name == 'SAKFinPicassoModule' or target.name == 'SAKIdentityCardRecognizer' or target.name == 'SAKFinVerificationCenter' or target.name == 'SAKBankCardRecognizer' or target.name == 'SAKHybridCashier' or target.name == 'SAKMesh' or target.name == 'SAKNeoHybrid' or target.name == 'SAKOneClickPayCashier' or target.name == 'MTSFBarcodeCashier' or target.name == 'SAKHybridMeituanPay'
                config.build_settings['USE_HEADERMAP'] = "NO"
                config.build_settings['CLANG_WARN_DOCUMENTATION_COMMENTS'] = 'NO'
                config.build_settings['GCC_TREAT_INCOMPATIBLE_POINTER_TYPE_WARNINGS_AS_ERRORS'] =  "YES"
                if config.name == 'ReleaseInhouse' or config.name == 'DailyBuild'
                    config.build_settings['GCC_GENERATE_TEST_COVERAGE_FILES'] = 'YES'
                    config.build_settings['GCC_INSTRUMENT_PROGRAM_FLOW_ARCS'] = 'YES'
                end
            end
        end
        target.build_configurations.each do |config|
            macros = ['$(inherited)']
            if config.name == 'Debug'
              macros << 'TEST=1'
	      config.build_settings['EXCLUDED_ARCHS'] = 'arm64'
            elsif config.name == 'ReleaseInhouse'
              macros << 'TEST=1' << 'NDEBUG=1'
            elsif config.name == 'DailyBuild'
              macros << 'TEST=1' << 'NDEBUG=1'
            elsif config.name == 'Release'
              macros << 'NDEBUG=1'
            end
            config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= macros
            config.build_settings['CLANG_WARN_DOCUMENTATION_COMMENTS'] = 'NO'
            config.build_settings['CLANG_WARN_STRICT_PROTOTYPES'] = 'NO'
        end
    end
end
