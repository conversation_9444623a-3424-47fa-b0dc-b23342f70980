# 源码集成
# pod 'SAKWallet', '5.7.0', :inhibit_warnings => false
# pod 'SAKCashier', '5.9.5', :inhibit_warnings => false
# pod 'SAKPaymentAccount', '5.7.0', :inhibit_warnings => false
# pod 'SAKHybridCashier', '2.2.2', :inhibit_warnings => false
# pod 'SAKBarcodeCashier', '5.7.0.1', :inhibit_warnings => false
# pod 'SAKPaymentChannel', '5.7.0', :inhibit_warnings => false
# pod 'SAKMeituanPay', '5.7.2', :inhibit_warnings => false
# pod 'SAKPaymentKit', '5.8.1', :inhibit_warnings => false
# pod 'SAKBankCardRecognizer', '1.2.1', :inhibit_warnings => false
# pod 'SAKIdentityCardRecognizer', '1.0.0', :inhibit_warnings => false
# pod 'SAKFinVerificationCenter', '1.3.1', :inhibit_warnings => false
# pod 'SAKFinanceThirdPayAdpater', '1.0.0', :inhibit_warnings => false
# pod 'SAKFinPicassoModule', '1.2.5', :inhibit_warnings => false
# pod 'SAKPaymentAdapter', '0.0.8', :inhibit_warnings => false
# 分支集成
# pod 'SAKLivenessDetection',:git => 'ssh://*******************/payios/saklivenessdetection.git', :branch => 'development'
# pod 'SAKFinanceUIKit', :git => 'ssh://*******************/payios/sakfinanceuikit.git', :branch => 'development'
# pod 'SAKFinanceDynamic',   :git => 'ssh://*******************/payios/sakfinancedynamic.git', :branch => 'release/1.1'

# 基础组件
pod 'CIPServiceRegistry', :git => 'ssh://*******************/met/cipserviceregistry.git', :branch => 'master', :inhibit_warnings => false
pod 'METServiceRegistry', :git => 'ssh://*******************/ios/metserviceregistry.git', :branch => 'master', :inhibit_warnings => false

# 三方组件
pod 'uppay',   :git => 'ssh://*******************/ios/uppay.git', :branch => 'master'
pod 'cip-alipay',   :git => 'ssh://*******************/ios/alipay.git', :branch => 'develop'
pod 'SAKCardNFCRecognizer',   :git => 'ssh://*******************/payios/sakcardnfcrecognizer.git', :branch => 'develop'

# 业务基础组件
pod 'SAKMesh', :git => 'ssh://*******************/payios/sakmesh', :branch => 'develop', :inhibit_warnings => false
pod 'SAKWallet', :git => 'ssh://*******************/ios/sakwallet.git', :branch => 'develop', :inhibit_warnings => false
pod 'SAKSiriPayment', :git => 'ssh://*******************/payios/saksiripayment.git', :branch => 'master', :inhibit_warnings => false

# 业务组件
# pod 'SAKIdentityCardRecognizer', :git => 'ssh://*******************/payios/sakidentitycardrecognizer.git', :branch => 'develop', :inhibit_warnings => false
pod 'MTSFBarcodeCashier', :git => 'ssh://*******************/quic/quickpass-qrcode-ios.git', :branch => 'develop', :inhibit_warnings => false
pod 'SAKBarcodeCashier', :git => 'ssh://*******************/ios/sakbarcodecashier.git', :branch => 'develop', :inhibit_warnings => false
pod 'SAKFinanceThirdPayAdpater', :git => 'ssh://*******************/payios/sakfinancethirdpayadpater.git', :branch => 'development', :inhibit_warnings => false
# pod 'SAKFinVerificationCenter', :git => 'ssh://*******************/payios/sakfinverificationcenter.git', :branch => 'develop', :inhibit_warnings => false
pod 'SAKFinPicassoModule', :git => 'ssh://*******************/payios/sakfinpicassomodule.git', :branch => 'development', :inhibit_warnings => false

pod 'SAKBankCardRecognizer', :git => 'ssh://*******************/payios/sakbankcardrecognizer.git', :branch => 'develop', :inhibit_warnings => false
# pod 'SAKPaymentGuard', :git => 'ssh://*******************/payios/sakpaymentguard.git', :branch => 'develop', :inhibit_warnings => false
# pod 'SAKNeoHybrid', :git => 'ssh://*******************/payios/sakneohybrid.git', :branch => 'develop', :inhibit_warnings => false
pod 'SAKHybridCashier', :git => 'ssh://*******************/payios/sakhybridcashier.git', :branch => 'develop', :inhibit_warnings => false
# pod 'SAKPaymentKit', :git => 'ssh://*******************/ios/sakpaymentkit.git', :branch => 'release/13.5.0', :inhibit_warnings => false
# pod 'SAKMeituanPay', :git => 'ssh://*******************/ios/sakmeituanpay.git', :branch => 'develop', :inhibit_warnings => false
pod 'SAKPaymentChannel', :git => 'ssh://*******************/ios/sakpaymentchannel.git', :branch => 'develop', :inhibit_warnings => false
# pod 'SAKOneClickPayCashier', :git => 'ssh://*******************/payios/sakoneclickpaycashier.git', :branch => 'develop', :inhibit_warnings => false
# pod 'SAKCashier', :git => 'ssh://*******************/ios/sakcashier.git', :branch => 'release/13.5.0', :inhibit_warnings => false
pod 'SAKPaymentAdapter', :git => 'ssh://*******************/payios/sakpaymentadapter.git', :branch => 'develop', :inhibit_warnings => false
pod 'SAKHybridMeituanPay', :git => 'ssh://*******************/payios/sakhybridmeituanpay.git', :branch => 'develop'
pod 'SAKPayRouter', :git => 'ssh://*******************/payios/sakpayrouter.git', :branch => 'develop', :inhibit_warnings => false

# 及时雨
# pod 'SAKKQClient', :git => 'ssh://*******************/ios/sakkqclient.git', :branch => 'develop', :inhibit_warnings => false

# 本地调试
pod 'SAKPaymentKit', :path => '../sakpaymentkit', :inhibit_warnings => false
# pod 'MTSFBarcodeCashier', :path => '../quickpass-qrcode-ios', :inhibit_warnings => false
# pod 'SAKBarcodeCashier', :path => '../sakbarcodecashier', :inhibit_warnings => false
pod 'SAKOneClickPayCashier', :path => '../sakoneclickpaycashier', :inhibit_warnings => false
pod 'SAKCashier', :path => '../sakcashier', :inhibit_warnings => false
# pod 'SAKPayRouter', :path => '../sakpayrouter', :inhibit_warnings => false

# pod 'SAKPaymentAdapter', :path => '../sakpaymentadapter', :inhibit_warnings => false
# pod 'SAKPaymentChannel', :path => '../sakpaymentchannel', :inhibit_warnings => false
pod 'SAKMeituanPay', :path => '../sakmeituanpay', :inhibit_warnings => false
# pod 'SAKFinPicassoModule', :path => '../sakfinpicassomodule', :inhibit_warnings => false
# pod 'SAKFinanceDynamic', :path => '..//paysakfinancedynamic, :inhibit_warnings => false'
pod 'SAKPaymentGuard', :path =>'../sakpaymentguard', :inhibit_warnings => false
pod 'SAKFinVerificationCenter', :path =>'../sakfinverificationcenter', :inhibit_warnings => false
# pod 'SAKHybridCashier', :path =>'../sakhybridcashier', :inhibit_warnings => false
pod 'SAKNeoHybrid', :path =>'../sakneohybrid' , :inhibit_warnings => false
# pod 'SAKHybridMeituanPay', :path=>'../sakhybridmeituanpay', :inhibit_warnings => false
# pod 'SAKPayRouter', :path=>'../sakpayrouter', :inhibit_warnings => false
pod 'SAKIdentityCardRecognizer', :path => '../sakidentitycardrecognizer', :inhibit_warnings => false
# pod 'SAKBankCardRecognizer', :path => '../sakbankcardrecognizer', :inhibit_warnings => false
# pod 'SAKKQClient', :path => '../sakkqclient', :inhibit_warnings => false
# pod 'cip-alipay', :path => '../alipay', :inhibit_warnings => false
